import React from "react";
import Image from "next/image";

type DailyQuoteCardProps = {
    quote: string;
    author: string;
    user: string;
    role: string;
};

export const DailyQuoteCard = ({
    quote,
    author,
    user,
    role,
}: DailyQuoteCardProps) => {
    return (
        <div className="relative text-center md:text-left lg:text-left bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-4 sm:p-6 md:p-8 overflow-hidden">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-green-500 text-2xl sm:text-4xl md:text-6xl font-extrabold opacity-10 uppercase tracking-wider">
                {role}
            </div>

            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start relative z-10 space-y-4 sm:space-y-0">
                <div className="flex-1 sm:pr-4 md:pr-8">
                    <blockquote className="text-gray-800 text-lg sm:text-xl md:text-2xl font-bold leading-relaxed mb-3 sm:mb-4">
                        {quote}
                        <span className="animate-ping">|</span>
                    </blockquote>

                    <p className="text-gray-600 text-xs sm:text-sm mb-4 sm:mb-6">
                        — {author}
                    </p>

                    <div className="mb-4 sm:mb-6">
                        <p className="text-gray-700 text-sm sm:text-base">
                            Welcome back,
                        </p>
                        <p className="text-gray-700 text-sm sm:text-base">
                            {user}!
                        </p>
                    </div>

                    <button className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 text-sm sm:text-base rounded-lg transition-colors duration-200 shadow-md w-full sm:w-auto">
                        Problems
                    </button>
                </div>

                <div className="flex-shrink-0 flex justify-center sm:justify-end">
                    <div className="relative">
                        <Image
                            src="/daily_a2sv-hub.svg"
                            alt="dashboard illustration"
                            width="120"
                            height="120"
                            className="w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36"
                            aria-hidden="true"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DailyQuoteCard;
