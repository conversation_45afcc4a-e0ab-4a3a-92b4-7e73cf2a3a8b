import React from "react";
import Image from "next/image";

type DailyQuoteCardProps = {
    quote: string;
    author: string;
    user: string;
    role: string;
};

export const DailyQuoteCard = ({
    quote,
    author,
    user,
    role,
}: DailyQuoteCardProps) => {
    return (
        <div className="relative bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-8 shadow-lg  overflow-hidden">
            <div className="absolute top-0 right-0 w-32 h-32 bg-green-300 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-green-400 rounded-full opacity-15 transform translate-x-12 translate-y-12"></div>

            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-green-500 text-6xl font-extrabold opacity-10 uppercase tracking-wider">
                {role}
            </div>

            <div className="flex justify-between items-start relative z-10">
                <div className="flex-1 pr-8">
                    <blockquote className="text-gray-800 text-2xl font-bold leading-relaxed mb-4">
                        {quote}
                    </blockquote>

                    <p className="text-gray-600 text-sm mb-6">— {author}</p>

                    <div className="mb-6">
                        <p className="text-gray-700 text-base">Welcome back,</p>
                        <p className="text-gray-800 font-semibold text-base">
                            {user}!
                        </p>
                    </div>

                    <button className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 shadow-md">
                        Problems
                    </button>
                </div>
                <div className="flex-shrink-0">
                    <div className="flex items-center justify-center absolute top-1/3 right-0">
                        <Image
                            src="/daily_a2sv-hub.svg"
                            alt="dashboard illustration"
                            width="110"
                            height="120"
                            className="w-full h-full"
                            aria-hidden="true"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DailyQuoteCard;
