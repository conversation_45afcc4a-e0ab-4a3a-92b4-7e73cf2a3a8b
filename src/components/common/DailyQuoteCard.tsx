import React from "react";
import Image from "next/image";

type DailyQuoteCardProps = {
    quote: string;
    author: string;
    user: string;
    role: string;
};

export const DailyQuoteCard = ({
    quote,
    author,
    user,
    role,
}: DailyQuoteCardProps) => {
    return (
        <div className="relative bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-4 sm:p-6 md:p-8 shadow-lg overflow-hidden">
            {/* Background decorative circles - responsive sizes */}
            <div className="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 bg-green-300 rounded-full opacity-20 transform translate-x-8 sm:translate-x-12 md:translate-x-16 -translate-y-8 sm:-translate-y-12 md:-translate-y-16"></div>
            <div className="absolute bottom-0 right-0 w-12 h-12 sm:w-18 sm:h-18 md:w-24 md:h-24 bg-green-400 rounded-full opacity-15 transform translate-x-6 sm:translate-x-9 md:translate-x-12 translate-y-6 sm:translate-y-9 md:translate-y-12"></div>

            {/* Background role text - responsive sizing and positioning */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-green-500 text-2xl sm:text-4xl md:text-6xl font-extrabold opacity-10 uppercase tracking-wider">
                {role}
            </div>

            {/* Main content - responsive layout */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start relative z-10 space-y-4 sm:space-y-0">
                <div className="flex-1 sm:pr-4 md:pr-8">
                    {/* Quote - responsive text sizing */}
                    <blockquote className="text-gray-800 text-lg sm:text-xl md:text-2xl font-bold leading-relaxed mb-3 sm:mb-4">
                        {quote}
                        <span className="animate-ping">|</span>
                    </blockquote>

                    {/* Author - responsive spacing */}
                    <p className="text-gray-600 text-xs sm:text-sm mb-4 sm:mb-6">
                        — {author}
                    </p>

                    {/* Welcome message - responsive spacing */}
                    <div className="mb-4 sm:mb-6">
                        <p className="text-gray-700 text-sm sm:text-base">
                            Welcome back,
                        </p>
                        <p className="text-gray-700 text-sm sm:text-base">
                            {user}!
                        </p>
                    </div>

                    {/* Button - responsive sizing */}
                    <button className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 text-xs sm:text-sm rounded-lg transition-colors duration-200 shadow-md w-full sm:w-auto">
                        Problems
                    </button>
                </div>

                {/* Image container - responsive positioning */}
                <div className="flex-shrink-0 flex justify-center sm:justify-end">
                    <div className="relative">
                        <Image
                            src="/daily_a2sv-hub.svg"
                            alt="dashboard illustration"
                            width="60"
                            height="60"
                            className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24"
                            aria-hidden="true"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DailyQuoteCard;
