import React, { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

type EventsCardProps = {
    title: string;
    date: string;
    time: string;
    currentIndex?: number;
    totalEvents?: number;
    onPrevious?: () => void;
    onNext?: () => void;
    targetDate?: Date;
};

const EventsCard = ({
    title,
    date,
    time,
    currentIndex = 1,
    totalEvents = 2,
    onPrevious,
    onNext,
    targetDate,
}: EventsCardProps) => {
    const [timeLeft, setTimeLeft] = useState({
        hours: 21,
        minutes: 47,
        seconds: 23,
    });

    useEffect(() => {
        if (targetDate) {
            const calculateTimeLeft = () => {
                const now = new Date().getTime();
                const eventTime = targetDate.getTime();
                const difference = eventTime - now;

                if (difference > 0) {
                    const hours = Math.floor(difference / (1000 * 60 * 60));
                    const minutes = Math.floor(
                        (difference % (1000 * 60 * 60)) / (1000 * 60)
                    );
                    const seconds = Math.floor(
                        (difference % (1000 * 60)) / 1000
                    );

                    setTimeLeft({ hours, minutes, seconds });
                } else {
                    setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
                }
            };

            calculateTimeLeft();
            const timer = setInterval(calculateTimeLeft, 1000);

            return () => clearInterval(timer);
        }
    }, [targetDate]);

    return (
        <div className="relative bg-white rounded-2xl p-6 border border-gray-100 max-w-sm mx-auto">
            {/* Navigation Header */}
            <div className="flex justify-between items-center mb-6">
                <button
                    onClick={onPrevious}
                    className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    aria-label="Previous event"
                >
                    <ChevronLeft className="w-5 h-5 text-gray-400" />
                </button>

                <div className="flex items-center space-x-1 text-gray-400 text-sm font-medium">
                    <span>{currentIndex}</span>
                    <span>/</span>
                    <span>{totalEvents}</span>
                </div>

                <button
                    onClick={onNext}
                    className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    aria-label="Next event"
                >
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                </button>
            </div>

            {/* Upcoming Badge */}
            <div className="flex justify-center mb-6">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-500 text-white">
                    Upcoming
                </span>
            </div>

            {/* Event Title */}
            <div className="text-center mb-6">
                <h2 className="text-lg sm:text-xl font-bold text-gray-900 leading-tight px-2">
                    {title}
                </h2>
            </div>

            {/* Countdown Timer */}
            <div className="text-center mb-6">
                <div className="text-md font-bold text-green-600 mb-2">
                    {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
                </div>
            </div>

            {/* Date and Time */}
            <div className="text-center text-gray-600">
                <div className="text-sm font-medium">
                    {date} | {time}
                </div>
            </div>
        </div>
    );
};

export default EventsCard;
