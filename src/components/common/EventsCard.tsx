import React, { useState, useEffect } from "react";
import {
    ChevronLeft,
    ChevronLeftIcon,
    ChevronRight,
    ChevronRightIcon,
} from "lucide-react";

type EventsCardProps = {
    title: string;
    date: string;
    time: string;
    currentIndex?: number;
    totalEvents?: number;
    onPrevious?: () => void;
    onNext?: () => void;
    targetDate?: Date;
};

const EventsCard = ({
    title,
    date,
    time,
    currentIndex = 1,
    totalEvents = 2,
    onPrevious,
    onNext,
    targetDate,
}: EventsCardProps) => {
    const [timeLeft, setTimeLeft] = useState({
        hours: 21,
        minutes: 47,
        seconds: 23,
    });

    useEffect(() => {
        if (targetDate) {
            const calculateTimeLeft = () => {
                const now = new Date().getTime();
                const eventTime = targetDate.getTime();
                const difference = eventTime - now;

                if (difference > 0) {
                    const hours = Math.floor(difference / (1000 * 60 * 60));
                    const minutes = Math.floor(
                        (difference % (1000 * 60 * 60)) / (1000 * 60)
                    );
                    const seconds = Math.floor(
                        (difference % (1000 * 60)) / 1000
                    );

                    setTimeLeft({ hours, minutes, seconds });
                } else {
                    setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
                }
            };

            calculateTimeLeft();
            const timer = setInterval(calculateTimeLeft, 1000);

            return () => clearInterval(timer);
        }
    }, [targetDate]);

    return (
        <div className=" bg-white rounded-xl p-4 sm:p-6 h-full">
            <div className="flex justify-end items-center">
                <button
                    onClick={onPrevious}
                    className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    aria-label="Previous event"
                >
                    <ChevronLeft className="w-4 h-4 text-gray-400" />
                </button>

                <div className="flex items-center space-x-1 text-gray-400 text-sm font-medium">
                    <span>{currentIndex}</span>
                    <span>/</span>
                    <span>{totalEvents}</span>
                </div>

                <button
                    onClick={onNext}
                    className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    aria-label="Next event"
                >
                    <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                </button>
            </div>

            <div className="flex justify-center mb-6">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-400 text-white">
                    Upcoming
                </span>
            </div>

            <div className="text-center px-4">
                <h2 className="text-xl font-bold text-gray-900 leading-tight">
                    {title}
                </h2>
            </div>

            <div className="text-center ">
                <div className="text-xl font-bold text-green-600 mt-2">
                    {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
                </div>
            </div>

            <div className="text-center ">
                <div className="text-sm font-medium">
                    {date} | {time}
                </div>
            </div>
        </div>
    );
};

export default EventsCard;
